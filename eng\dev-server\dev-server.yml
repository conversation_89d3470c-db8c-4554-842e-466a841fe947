build-deploy-job:
  stage: build-deploy
  when: always
  tags:
    - dev
  script:
    - if (sudo systemctl is-active teslametrics); then sudo systemctl stop teslametrics; fi
    - sudo rm -rf /home/<USER>/app 
    - cd src/Teslametrics.App.Web
    - dotnet publish -c Release --output /home/<USER>/app
    - cd ../..
    - sudo cp eng/dev-server/teslametrics.service /etc/systemd/system/
    - sudo systemctl daemon-reload
    - sudo systemctl start teslametrics

migrate-db:
  stage: migration
  when: manual
  tags:
    - dev
  script:
    - export ASPNETCORE_ENVIRONMENT=Development
    - cd src/Teslametrics.DbMigrator
    - dotnet run