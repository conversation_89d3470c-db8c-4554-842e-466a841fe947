using CsvHelper.Configuration;
using Teslametrics.App.Web.Components.CSVImport;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.ImportDialog;

public partial class ImportDialog
{
	private Guid _organizationId;

	private class CameraImportDto
	{
		public string Name { get; set; } = string.Empty;
		public string Directory { get; set; } = string.Empty;
		public string Quota { get; set; } = string.Empty;
		public string ArchiveUri { get; set; } = string.Empty;
		public string ViewUri { get; set; } = string.Empty;
		public string PublicUri { get; set; } = string.Empty;
		public TimeSpan TimeZone { get; set; } = TimeSpan.FromHours(3);
		public Coordinates? Coordinates { get; set; }
		public bool AutoStart { get; set; }
		public bool StartOnCreate { get; set; }
		public bool IsValid { get; set; } = true;
		public bool HasQuotaError { get; set; } = false;
		public string? QuotaErrorMessage { get; set; }
	};

	private MudBlazor.DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MudBlazor.MaxWidth.Large, NoHeader = true };
	private bool _isVisible;

	private int ActivePanelIndex = 0;
	private IEnumerable<CameraImportDto>? _importModels;

	private List<CsvExpectedFields> _expectedFields = [
		new (nameof(CameraImportDto.Name), "Название камеры", true),
		new (nameof(CameraImportDto.Directory), "Название директории", false),
		new (nameof(CameraImportDto.Quota), "Квота", true),
		new (nameof(CameraImportDto.ViewUri), "Ссылка на поток для видов", true),
		new (nameof(CameraImportDto.PublicUri), "Ссылка на публичный поток", true),
		new (nameof(CameraImportDto.ArchiveUri), "Ссылка на архивный поток", true),
		new (nameof(CameraImportDto.AutoStart), "Автозапуск при перезапуске системы", false),
	];

	protected override void OnInitialized()
	{
		base.OnInitialized();

		CompositeDisposable.Add(EventSystem.Subscribe<CameraListImportEto>(OnEventHandler));
	}

	protected void OnError(Exception exc)
	{
		Snackbar.Add($"При импорте произошла ошибка: {exc.Message}", MudBlazor.Severity.Error);
	}

	private ImportMap GetImportMap(IEnumerable<CsvMapperHeader> csvFields)
	{
		return new ImportMap(csvFields);
	}

	private class ImportMap : ClassMap<CameraImportDto>
	{
		public ImportMap(IEnumerable<CsvMapperHeader> csvFields)
		{
			Map(m => m.Name).Name(csvFields.FirstOrDefault(field => field.MappedField == nameof(CameraImportDto.Name))?.Name);

			var directoryField = csvFields.FirstOrDefault(field => field.MappedField == nameof(CameraImportDto.Directory));
			if (directoryField is not null && directoryField.Name is not null)
				Map(m => m.Directory).Name(directoryField.Name);

			var quotaField = csvFields.FirstOrDefault(field => field.MappedField == nameof(CameraImportDto.Quota));
			if (quotaField is not null && quotaField.Name is not null)
				Map(m => m.Quota).Name(quotaField.Name);

			var archiveUriField = csvFields.FirstOrDefault(field => field.MappedField == nameof(CameraImportDto.ArchiveUri));
			if (archiveUriField is not null && archiveUriField.Name is not null)
				Map(m => m.ArchiveUri).Name(archiveUriField.Name);

			var viewUriField = csvFields.FirstOrDefault(field => field.MappedField == nameof(CameraImportDto.ViewUri));
			if (viewUriField is not null && viewUriField.Name is not null)
				Map(m => m.ViewUri).Name(viewUriField.Name);

			var publicUriField = csvFields.FirstOrDefault(field => field.MappedField == nameof(CameraImportDto.PublicUri));
			if (publicUriField is not null && publicUriField.Name is not null)
				Map(m => m.PublicUri).Name(publicUriField.Name);

			var autoStartField = csvFields.FirstOrDefault(field => field.MappedField == nameof(CameraImportDto.AutoStart));
			if (autoStartField is not null && autoStartField.Name is not null)
				Map(m => m.AutoStart).Name(autoStartField.Name);
		}
	}

	private async Task OnImport(IEnumerable<CameraImportDto> importModels)
	{
		_importModels = importModels;
		if (_importModels is null)
		{
			ActivePanelIndex = 0;
			return;
		}
		Snackbar.Add($"Найдено {_importModels.Count()} сущностей");
		ActivePanelIndex = 1;
		await CheckValid();
	}

	private Task<bool> IsValid()
	{
		return Task.FromResult(true);
		// if (ImportModels is null) return false;

		// var tasks = ImportModels.Select(model => Validator.ValidateAsync(model));
		// var results = await Task.WhenAll(tasks);
		// return !results.Any(model => !model.IsValid);
	}

	private async Task<bool> CheckValid()
	{
		bool isValid = await IsValid();
		if (!isValid)
		{
			Snackbar.Add("Некоторые из моделей имеют ошибки, пожалуйста, исправьте их перед импортом");
		}
		return isValid;
	}

	private async Task SubmitAsync()
	{
		if (_importModels is null)
		{
			return;
		}

		ImportCameraListUseCase.Response? response = null;
		try
		{
			response = await ScopeFactory.MediatorSend(new ImportCameraListUseCase.Command(_organizationId, _importModels!.Select(m => new ImportCameraListUseCase.Command.Camera(m.Name, TimeSpan.Zero, null, m.Directory, m.ArchiveUri, m.ViewUri, m.PublicUri, m.Quota, false, false)).ToList()));
		}
		catch (Exception exc)
		{
			response = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось импортировать камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
		}

		if (response is null) return;
		switch (response.Result)
		{
			case ImportCameraListUseCase.Result.Success:
				if (response.CamerasWithIncorrectQuota.Count == 0)
				{
					Snackbar.Add("Камеры успешно импортированы", MudBlazor.Severity.Success);
					Cancel();
				}
				else
				{
					Snackbar.Add($"Камеры успешно импортированы, но не удалось импортировать {response.CamerasWithIncorrectQuota.Count} камер из-за ошибки в квоте", MudBlazor.Severity.Error);

					// Словарь для быстрого поиска ошибок
					var incorrectQuotaSet = new HashSet<string>(response.CamerasWithIncorrectQuota);
					var quotaLimitReachedSet = new HashSet<string>(response.CamerasQuotaLimitReached);

					// Очистить и обновить список с ошибками
					var updatedImportModels = new List<CameraImportDto>();

					foreach (var camera in _importModels)
					{
						if (incorrectQuotaSet.Contains(camera.Name))
						{
							camera.HasQuotaError = true;
							camera.QuotaErrorMessage = "Квота не найдена";
							updatedImportModels.Add(camera);
						}
						else if (quotaLimitReachedSet.Contains(camera.Name))
						{
							camera.HasQuotaError = true;
							camera.QuotaErrorMessage = "Лимит квоты исчерпан";
							updatedImportModels.Add(camera);
						}
					}

					// Обновить список
					_importModels = updatedImportModels;
				}
				break;
			case ImportCameraListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при импорте камер", MudBlazor.Severity.Error);
				break;
			case ImportCameraListUseCase.Result.OrganizationNotFound:
				Snackbar.Add("Организация не найдена", MudBlazor.Severity.Error);
				break;
			case ImportCameraListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(ImportDialog), nameof(ImportCameraListUseCase));
				Snackbar.Add($"Не удалось импортировать камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(ImportDialog), nameof(ImportCameraListUseCase), response.Result);
				Snackbar.Add($"Не удалось импортировать камеры из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;

		}

	}

	private void Cancel()
	{
		_isVisible = false;
		_importModels = [];
	}

	private void OnEventHandler(CameraListImportEto eto)
	{
		_isVisible = true;
		ActivePanelIndex = 0;
		_organizationId = eto.OrganizationId;
		StateHasChanged();
	}
}
