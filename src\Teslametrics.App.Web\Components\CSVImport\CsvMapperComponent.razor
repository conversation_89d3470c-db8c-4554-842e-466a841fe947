﻿@using System.Text;
@using CsvHelper.Configuration;
@using System.Globalization;
@using CsvHelper;

@typeparam TImportModel where TImportModel : class
@typeparam TImportMap where TImportMap : ClassMap<TImportModel>

<MudStack>
	@if (FileContentBytes is null)
	{
		<FileUploadComponent UploadFileButtonText="@LocaleStrings.UploadFileButtonText"
							 UploadFileContainerText="@LocaleStrings.UploadFileContainerText"
							 OnFileRead="OnFileRead"
							 OnReset="OnFileReset" />
	}
	@if (FileContentBytes is not null)
	{
		<MudGrid Class="align-end">
			<MudItem Class="ml-n4"
					 xs="3">
				<MudIconButton Icon="@Icons.Material.Filled.ArrowBackIosNew"
							   OnClick="OnFileReset"
							   Title="@LocaleStrings.ResetFileButtonText" />
			</MudItem>
			<MudItem xs="9">
				<MudSelect T="string"
						   Disabled="false"
						   ReadOnly="false"
						   Value="@Delimeter"
						   ValueChanged="DelimeterChanged"
						   Label="@LocaleStrings.DelimeterSelectLabel"
						   Immediate="true">
					<MudSelectItem T="string"
								   Value="@(",")">,</MudSelectItem>
					<MudSelectItem T="string"
								   Value="@(";")">;</MudSelectItem>
				</MudSelect>
				@* <CsvDelimeterSelectComponent Value="@Delimeter"
											 ValueChanged="DelimeterChanged"
											 Label="@LocaleStrings.DelimeterSelectLabel"
											 Clearable="true"
											 Immediate="true" /> *@
			</MudItem>
		</MudGrid>

		<HeaderMapperComponent ExpectedHeaders="@ExpectedHeaders"
							   CsvHeaders="Headers"
							   IsValidChanged="() => StateHasChanged()"
							   LocaleStrings="@LocaleStrings.Mapping"
							   @ref="_mapperRef" />

		<MudStack Row="true"
				  Justify="Justify.FlexEnd">
			<MudButton Disabled="!_mapperRef?.IsValid ?? true"
					   OnClick="Import">@LocaleStrings.ConfirmHeaderMapping</MudButton>
		</MudStack>
	}
</MudStack>
