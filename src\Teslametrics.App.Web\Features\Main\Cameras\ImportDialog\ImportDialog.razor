﻿@using CsvHelper.Configuration
@using MudExtensions
@using MudExtensions.Utilities
@using Teslametrics.App.Web.Events.Cameras
@using Teslametrics.App.Web.Features.Main.Cameras.ImportDialog.QoutaSelect
@inherits InteractiveBaseComponent
<MudDialog @bind-Visible="_isVisible"
           ActionsClass="mx-2"
           ContentClass="px-4 mx-0"
           Options="_dialogOptions">
    <DialogContent>
        <MudTabs Elevation="0"
                 ApplyEffectsToContainer="true"
                 PanelClass="pt-6"
                 KeepPanelsAlive="true"
                 @bind-ActivePanelIndex="ActivePanelIndex">
            <MudTabPanel Text="Маппинг модели">
                <CsvMapperComponent TImportModel="CameraImportDto"
                                    TImportMap="ImportMap"
                                    ExpectedHeaders="@_expectedFields"
                                    OnParseException="OnError"
                                    GetImportMap="GetImportMap"
                                    OnImport="OnImport" />
            </MudTabPanel>
            <MudTabPanel Text="Проверка сущностей"
                         Disabled="_importModels is null">
                <MudDataGrid T="CameraImportDto"
                             Items="@_importModels"
                             ReadOnly="@(_importModels is null)"
                             EditMode="@DataGridEditMode.Cell"
                             SortMode="SortMode.None"
                             Elevation="0"
                             Virtualize="true"
                             FixedHeader="true"
                             Height="350px"
                             HorizontalScrollbar="true"
                             ColumnResizeMode="ResizeMode.Container">
                    <Columns>
                        <PropertyColumn Property="x => x.Name"
                                        Title="Название">
                            <EditTemplate>
                                <MudTextField @bind-Value="context.Item.Name" />
                            </EditTemplate>
                        </PropertyColumn>
                        <PropertyColumn Property="x => x.Directory"
                                        Title="Директория" />
                        <PropertyColumn Property="x => x.Quota"
                                        Title="Квота">
                            <EditTemplate>
                                <QuotaFieldComponent @bind-Value="context.Item.Quota"
                                                     OrganizationId="_organizationId"
                                                     Error="@context.Item.HasQuotaError"
                                                     ErrorText="@context.Item.QuotaErrorMessage" />
                            </EditTemplate>
                        </PropertyColumn>
                        <PropertyColumn Property="x => x.ArchiveUri"
                                        Title="Ссылка на архивный поток" />
                        <PropertyColumn Property="x => x.ViewUri"
                                        Title="Ссылка на поток для видов" />
                        <PropertyColumn Property="x => x.PublicUri"
                                        Title="Ссылка на публичный поток" />
                        <PropertyColumn Property="x => x.AutoStart"
                                        Title="Автозапуск при перезапуске системы">
                            <EditTemplate>
                                <MudCheckBox @bind-Value="context.Item.AutoStart" />
                            </EditTemplate>
                        </PropertyColumn>
                        <PropertyColumn Property="x => x.StartOnCreate"
                                        Title="Запуск при создании">
                            <EditTemplate>
                                <MudCheckBox @bind-Value="context.Item.StartOnCreate" />
                            </EditTemplate>
                        </PropertyColumn>
                    </Columns>
                    <PagerContent>
                        <MudDataGridPager T="CameraImportDto"
                                          RowsPerPageString="Строк на странице:"
                                          InfoFormat="{first_item}-{last_item} из {all_items}" />
                    </PagerContent>
                </MudDataGrid>
            </MudTabPanel>
        </MudTabs>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel"
                   Variant="Variant.Text">Отменить</MudButton>
        <MudButton OnClick="SubmitAsync"
                   Variant="Variant.Outlined"
                   Disabled="@(!_importModels?.Any() ?? true || IsLoading)"
                   Color="Color.Secondary">Импорт</MudButton>
    </DialogActions>
</MudDialog>
