@inherits InteractiveBaseComponent

<MudAutocomplete T="string"
				 Label="Квота"
				 Value="Value"
				 DebounceInterval="500"
				 ValueChanged="OnSelectedValudeChanged"
				 SearchFunc="@SearchAsync"
				 ToStringFunc="item => item ?? string.Empty"
				 MaxItems="25"
				 Required="true"
				 AdornmentIcon="@Icons.Material.Filled.Search"
				 Clearable="true"
				 Error="@Error"
				 ErrorText="@ErrorText"
				 ListItemClass="pa-0"
				 RequiredError="Поле обязательно к заполнению"
				 SelectOnActivation="false"
				 ShowProgressIndicator="true"
				 ProgressIndicatorColor="Color.Primary">
	<NoItemsTemplate>
		<NoItemsFoundComponent HasItems="false" />
	</NoItemsTemplate>
</MudAutocomplete>