using System.Collections.Concurrent;
using Confluent.Kafka;
using FFMpegNET;
using Orleans.Streams;
using static Teslametrics.App.Web.Orleans.Camera.ICameraStreamGrain;

namespace Teslametrics.App.Web.Orleans.Camera;

public class CameraStreamGrain : Grain, ICameraStreamGrain
{
    private readonly ILogger _logger;
    private readonly RtspMicroSegmenter _microSegmenter;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly OrleansKafkaSerializer _serializer;
    private CameraStreamState _cameraStreamState = CameraStreamState.Disconnected;
    private readonly ConcurrentQueue<CameraStreamSignal> _externalQueue;
    private Guid _cameraId;
    private string _uri = string.Empty;
    private StreamType _streamType;
    private IAsyncStream<MicroSegment>? _videoStream;
    private Task? _processTask;
    private Task? _runTask;
    private IProducer<Null, MicroSegment>? _producer;

    private ICameraStreamRecorderGrain? _streamRecorderGrain;
    // private ICameraStreamPreviewGrain? _previewGrain;
    private readonly CancellationTokenSource _cts;

    public CameraStreamGrain(ILogger<CameraGrain> logger,
                             RtspMicroSegmenter microSegmenter,
                             IServiceScopeFactory serviceScopeFactory,
                             OrleansKafkaSerializer serializer)
    {
        _logger = logger;
        _microSegmenter = microSegmenter;
        _serviceScopeFactory = serviceScopeFactory;
        _serializer = serializer;

        _cts = new CancellationTokenSource();

        _externalQueue = new ConcurrentQueue<CameraStreamSignal>();
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        var config = new ProducerConfig
        {
            BootstrapServers = "localhost:9094",
            Acks = Acks.All,
            EnableIdempotence = true,
            MessageSendMaxRetries = 3,
            RetryBackoffMs = 100
        };

        _producer = new ProducerBuilder<Null, MicroSegment>(config).SetValueSerializer(_serializer).Build();

        var provider = this.GetStreamProvider(StreamNames.VideoLiveStream);
        _videoStream = provider.GetStream<MicroSegment>(StreamId.Create(StreamNamespaces.CameraStreams, this.GetPrimaryKey()));

        _processTask = Task.Run(() => ProcessAsync(_cts.Token));

        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        try
        {
            await StopProcessAsync();
            _microSegmenter.Dispose();

            _cts.Cancel();
            await _processTask!;

            _producer!.Dispose();

            await base.OnDeactivateAsync(reason, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Deactivation was canceled for camera stream. CameraId: {CameraId}, StreamType: {StreamType}", _cameraId, _streamType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during deactivation for camera stream. CameraId: {CameraId}, StreamType: {StreamType}", _cameraId, _streamType);
        }
    }

    public async Task<CameraStreamState> GetStatusAsync()
    {
        if (_streamRecorderGrain is not null)
        {
            await _streamRecorderGrain.PingAsync();
        }

        return _cameraStreamState;
    }

    public Task ConnectAsync(ConnectRequest request)
    {
        _cameraId = request.CameraId;
        _uri = request.Uri;
        _streamType = request.StreamType;

        _externalQueue.Enqueue(CameraStreamSignal.Connect);

        return Task.CompletedTask;
    }

    public Task DisconnectAsync()
    {
        _externalQueue.Enqueue(CameraStreamSignal.Disconnect);
        return Task.CompletedTask;
    }

    private async Task ProcessAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            Update();

            if (!_externalQueue.TryDequeue(out var signal))
            {
                await Task.Delay(100);
                continue;
            }

            switch (signal)
            {
                case CameraStreamSignal.Connect:
                    {
                        switch (_cameraStreamState)
                        {
                            case CameraStreamState.Disconnected:
                                {
                                    await ChangeStateAsync(CameraStreamState.Connecting);
                                    await StartProcessAsync(cancellationToken);
                                    break;
                                }
                        }

                        break;
                    }
                case CameraStreamSignal.Connected:
                    {
                        switch (_cameraStreamState)
                        {
                            case CameraStreamState.Connecting:
                            case CameraStreamState.Reconnecting:
                                {
                                    await ChangeStateAsync(CameraStreamState.Connected);
                                    break;
                                }
                        }

                        break;
                    }
                case CameraStreamSignal.Disconnect:
                    {
                        switch (_cameraStreamState)
                        {
                            case CameraStreamState.Connected:
                            case CameraStreamState.Connecting:
                            case CameraStreamState.Reconnecting:
                                {
                                    await StopProcessAsync();
                                    await ChangeStateAsync(CameraStreamState.Disconnected);
                                    break;
                                }
                        }

                        break;
                    }
                case CameraStreamSignal.Reconnect:
                    {
                        switch (_cameraStreamState)
                        {
                            case CameraStreamState.Connecting:
                            case CameraStreamState.Reconnecting:
                                {
                                    await StartProcessAsync(cancellationToken);
                                    break;
                                }
                            case CameraStreamState.Connected:
                                {
                                    await ChangeStateAsync(CameraStreamState.Reconnecting);
                                    await StartProcessAsync(cancellationToken);
                                    break;
                                }
                        }

                        break;
                    }
            }
        }
    }

    private void Update()
    {
        switch (_cameraStreamState)
        {
            case CameraStreamState.Connecting:
            case CameraStreamState.Reconnecting:
                {
                    if (_runTask is null)
                    {
                        _externalQueue.Enqueue(CameraStreamSignal.Reconnect);
                    }

                    break;
                }
            case CameraStreamState.Connected:
                {
                    if (_runTask!.IsCompleted)
                    {
                        _logger.LogError("RTSP processor failed for camera {Uri}", _uri);

                        _runTask!.Dispose();
                        _runTask = null;
                        _externalQueue.Enqueue(CameraStreamSignal.Reconnect);
                    }

                    break;
                }
        }
    }

    private async Task StartProcessAsync(CancellationToken cancellationToken)
    {
        try
        {
            // using var scope = _serviceScopeFactory.CreateScope();
            // if (_streamType is StreamType.Archive || _streamType is StreamType.View)
            // {
            //     _previewGrain = scope.ServiceProvider.GetRequiredService<ICameraStreamPreviewGrain>();
            // }

            if (_streamType is StreamType.Archive)
            {
                using var scope = _serviceScopeFactory.CreateScope();
                var grainFactory = scope.ServiceProvider.GetRequiredService<IGrainFactory>();
                _streamRecorderGrain = grainFactory.GetGrain<ICameraStreamRecorderGrain>(_cameraId);
                await _streamRecorderGrain.StartAsync();
            }

            // var grainFactory = scope.ServiceProvider.GetRequiredService<IGrainFactory>();
            // _streamRecorderGrain = grainFactory.GetGrain<ICameraStreamRecorderGrain>(_cameraId);
            // await _streamRecorderGrain.StartAsync();

            Func<MicroSegment, Task> onNextSegment = _streamType.HasFlag(StreamType.Archive)
                ? OnNextArchiveSegmentAsync
                : OnNextLiveSegmentAsync;

            // Func<Segment, Task> onNextSegment = _streamType.HasFlag(StreamType.Archive)
            //     ? OnNextArchiveSegmentAsync
            //     : OnNextLiveSegmentAsync;

            // _runTask = _rtspProcessor.Run(_uri, enableAudio: false, async segment =>
            // {
            //     var microSegment = new MicroSegment(segment.Stream.ToArray(), segment.StartTime, segment.Duration);
            //     await onNextSegment(microSegment);
            // }, OnNextPreview, cancellationToken);

            _runTask = _microSegmenter.Run(_uri, enableAudio: false, async segment =>
            {
                var microSegment = new MicroSegment(segment.Payload, segment.StartTime, segment.Duration);
                await onNextSegment(microSegment);
            }, cancellationToken);

            if (_cameraStreamState is not CameraStreamState.Connected)
            {
                _externalQueue.Enqueue(CameraStreamSignal.Connected);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting RTSP processor for URI: {Uri}", _uri);
        }
    }

    private async Task StopProcessAsync()
    {
        if (_runTask is not null)
        {

            //_rtspProcessor.RequestGracefulStop();
            _microSegmenter.RequestGracefulStop();

            await _streamRecorderGrain!.StopAsync();

            await _runTask!;
            _runTask = null;
        }
    }

    private async Task OnNextArchiveSegmentAsync(MicroSegment segment)
    {
        try
        {
            var topic = _cameraId.ToString();

            var message = new Message<Null, MicroSegment>
            {
                Value = segment
            };

            var deliveryResult = await _producer!.ProduceAsync(topic, message);

            await OnNextLiveSegmentAsync(segment);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending segment to Kafka. CameraId: {CameraId}, StreamType: {StreamType}", _cameraId, _streamType);
            await OnNextLiveSegmentAsync(segment);
        }
    }

    private async Task OnNextLiveSegmentAsync(MicroSegment segment)
    {
        try
        {
            await _videoStream!.OnNextAsync(segment);
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Error sending segment to live stream. CameraId: {CameraId}, StreamType: {StreamType}", _cameraId, _streamType);
        }
    }

    // private async Task OnNextArchiveSegmentAsync(Segment segment)
    // {
    //     var startTime = segment.StartTime;
    //     var endTime = startTime.AddSeconds(segment.Duration);

    //     var tags = new Dictionary<string, string>
    //     {
    //         { "StartTime", startTime.ToUnixTimeMilliseconds().ToString() },
    //         { "EndTime", endTime.ToUnixTimeMilliseconds().ToString() },
    //         { "Duration", segment.Duration.ToString(CultureInfo.InvariantCulture) }
    //     };

    //     var fileName = segment.StartTime.ToString("dd-MM-yyyy_HH-mm-ss-fff") + ".ts";

    //     try
    //     {
    //         // Create a memory stream with known length to avoid ObjectSize error
    //         var memoryStream = new MemoryStream();
    //         segment.Stream.Position = 0;
    //         await segment.Stream.CopyToAsync(memoryStream);
    //         memoryStream.Position = 0;

    //         // Upload the file with the memory stream that has a known length
    //         await _fileStorage.UploadFileAsync(_cameraId.ToString("N"), fileName, memoryStream, tags);

    //         // Reset position for the live segment processing
    //         segment.Stream.Position = 0;
    //         await OnNextLiveSegmentAsync(segment);
    //     }
    //     catch (Exception ex)
    //     {
    //         _logger.LogError(ex, "Error uploading segment to storage: {FileName}", fileName);

    //         // Still try to process the live segment even if storage upload fails
    //         segment.Stream.Position = 0;
    //         await OnNextLiveSegmentAsync(segment);
    //     }
    // }

    // private async Task OnNextLiveSegmentAsync(Segment segment)
    // {
    //     var memoryStream = new MemoryStream((int)segment.Stream.Length);
    //     segment.Stream.Position = 0;

    //     await segment.Stream.CopyToAsync(memoryStream);

    //     _streamCache.AddSegment(_streamIndex, memoryStream.GetBuffer(), segment.StartTime, segment.StartTime.AddSeconds(segment.Duration));
    // }

    private async Task ChangeStateAsync(CameraStreamState state)
    {
        if (_cameraStreamState != state)
        {
            _cameraStreamState = state;

            // // TODO: Заменить Outbox
            // List<object> @event = [_cameraStreamState switch
            // {
            //     CameraStreamState.Disconnected => new CameraStreamDisconnectedEvent(_cameraId, _uri),
            //     CameraStreamState.Connected => new CameraStreamConnectedEvent(_cameraId, _uri),
            //     CameraStreamState.Connecting => new CameraStreamConnectingEvent(_cameraId, _uri),
            //     CameraStreamState.Reconnecting => new CameraStreamReconnectingEvent(_cameraId, _uri),
            //     _ => throw new AppException("Invalid camera stream state")
            // }];

            // using var scope = _serviceScopeFactory.CreateScope();
            // var outbox = scope.ServiceProvider.GetRequiredService<IOutbox>();
            // await outbox.AddRangeAsync(@event);
        }

        await Task.CompletedTask;
    }
}

public enum CameraStreamState
{
    Disconnected,
    Connecting,
    Connected,
    Reconnecting
}

public enum CameraStreamSignal
{
    Disconnect,
    Connect,
    Connected,
    Reconnect
}