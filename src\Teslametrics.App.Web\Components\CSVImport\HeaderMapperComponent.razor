﻿<MudDropContainer T="CsvMapperHeader"
				  Items="@CsvHeaders"
				  ItemsSelector="@ItemSelector"
				  ItemDropped="OnDrop"
				  Class="d-flex flex-column gap-4"
				  @ref="_containerRef">
	<ChildContent>
		<MudStack>
			<MudText Typo="Typo.h6">@LocaleStrings.FileHeadersTitle</MudText>
			<MudDropZone T="CsvMapperHeader"
						 Identifier="@(string.Empty)"
						 DraggingClass="mud-alert-text-info"
						 ItemDraggingClass="mud-alert-text-info"
						 Class="rounded-lg border-2 border-dashed mud-border-lines-default pa-2 flex-row d-flex align-center gap-4"
						 Style="min-height: 72px;">
			</MudDropZone>
		</MudStack>

		<MudStack>
			<MudText Typo="Typo.h6">@LocaleStrings.EntityFieldsTitle</MudText>
			<div class="fields_container">
				@foreach (var item in ExpectedHeaders)
				{
					<MudBadge Icon="@Icons.Material.Filled.Warning"
							  Color="Color.Error"
							  Overlap="true"
							  Visible="@(item.Required && item.AssociatedField is null)">
						<MudDropZone T="CsvMapperHeader"
									 Identifier="@item.Name"
									 DraggingClass="mud-alert-text-info"
									 CanDrop="@((x) => (!CsvHeaders.Any(header => header.MappedField == item.Name)))"
									 ItemDraggingClass="mud-alert-text-error"
									 Class="rounded-lg border-2 border-dashed mud-border-lines-default pa-2 item">
							<ItemRenderer>
								<MudPaper Elevation="3"
										  Class="pa-3 rounded-lg d-flex align-center my-2"
										  MinHeight="48px">@context.Name</MudPaper>
							</ItemRenderer>
							<ChildContent>
								<MudText Typo="Typo.subtitle1">
									<b>@item.Label</b>
									@if (item.Required)
									{
										<b>*</b>
									}
								</MudText>
								@if (!CsvHeaders.Any(header => header.MappedField == item.Name))
								{
									<MudPaper Elevation="0"
											  Class="pa-2 my-2 d-flex flex-column mud-background-gray rounded-lg"
											  MinHeight="48px">
										<MudText Typo="Typo.overline">@LocaleStrings.DropZoneText</MudText>
									</MudPaper>
								}
							</ChildContent>

						</MudDropZone>
					</MudBadge>
				}
			</div>
		</MudStack>
	</ChildContent>
	<ItemRenderer>
		<MudPaper Elevation="3"
				  Class="pa-3 rounded-lg d-flex align-center"
				  MinHeight="48px">@context.Name</MudPaper>
	</ItemRenderer>

</MudDropContainer>

@code {
	private MudDropContainer<CsvMapperHeader>? _containerRef;

	[Parameter, EditorRequired]
	public IEnumerable<CsvExpectedFields> ExpectedHeaders { get; set; } = null!;

	[Parameter, EditorRequired]
	public IEnumerable<CsvMapperHeader> CsvHeaders { get; set; } = null!;

	public bool IsValid => ExpectedHeaders.All(header => header.Required ? header.AssociatedField is not null : true);

	[Parameter]
	public EventCallback<bool> IsValidChanged { get; set; }

	[Parameter]
	public HeaderMapperLocales LocaleStrings { get; set; } = new();

	protected override Task OnParametersSetAsync() => InvokeAsync(() =>
	{
		bool changed = !ExpectedHeaders.Any(expectedHeader => expectedHeader.AssociatedField is not null);
		foreach (var expectedHeader in ExpectedHeaders.Where(expectedHeader => expectedHeader.AssociatedField is not null))
		{
			var header = CsvHeaders.FirstOrDefault(header => header.Name == expectedHeader.AssociatedField!.Name);
			if (header is null)
			{
				expectedHeader.AssociatedField = null;
				changed = true;
			}
			else
			{
				header.MappedField = expectedHeader.Name;
				expectedHeader.AssociatedField = header;
			}
		}

		if (_containerRef is not null && changed)
		{
			_containerRef.Refresh();
		}
	});

	private async void OnDrop(MudItemDropInfo<CsvMapperHeader> mudCSVField)
	{
		if (mudCSVField is null || mudCSVField.Item is null) return;

		string? oldMappedField = mudCSVField.Item.MappedField;
		var oldHeader = ExpectedHeaders.FirstOrDefault(header => header.Name == oldMappedField);
		if (oldHeader is not null)
		{
			oldHeader.AssociatedField = null;
		}

		mudCSVField.Item.MappedField = mudCSVField.DropzoneIdentifier;
		var header = ExpectedHeaders.FirstOrDefault(header => header.Name == mudCSVField.DropzoneIdentifier);
		if (header is not null)
		{
			header.AssociatedField = mudCSVField.Item;
		}

		if (IsValidChanged.HasDelegate)
		{
			await IsValidChanged.InvokeAsync(IsValid);
		}
	}

	private static bool ItemSelector(CsvMapperHeader item, string identifier)
	{
		return item.MappedField == identifier;
	}
}
