﻿using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.AspNetCore.Components;
using System.Globalization;
using System.Text;

namespace Teslametrics.App.Web.Components.CSVImport;

public class CsvMapperHeader(string name, string? mappedField = null)
{
	public string Name { get; set; } = name;
	public string MappedField { get; set; } = mappedField ?? string.Empty;
}

public class CsvExpectedFields(string name, string label, bool required = false)
{
	public string Name { get; set; } = name;
	public string Label { get; set; } = label;
	public bool Required { get; set; } = required;

	public CsvMapperHeader? AssociatedField { get; set; }
}

public class HeaderMapperLocales
{
	public string EntityFieldsTitle { get; set; } = "Поля сущности";
	public string FileHeadersTitle { get; set; } = "Заголовки файла импорта";
	public string DropZoneText { get; set; } = "Перенесите заголовок сюда";
}

public class CsvMapperLocales
{
	public string DelimeterSelectLabel { get; set; } = "Разделитель";
	public string UploadFileButtonText { get; set; } = "Выберите файл с расширением .csv";
	public string UploadFileContainerText { get; set; } = "или перенесите файл в эту область";
	public string ResetFileButtonText { get; set; } = "К выбору файла";
	public string ConfirmHeaderMapping { get; set; } = "Подтвердить заголовки";

	public HeaderMapperLocales Mapping { get; set; } = new();
}

public partial class CsvMapperComponent<TImportModel, TImportMap>
	where TImportModel : class
	where TImportMap : ClassMap<TImportModel>
{
	private HeaderMapperComponent? _mapperRef;
	private byte[]? FileContentBytes;
	private string? Delimeter = ",";

	#region Parameters
	[Parameter, EditorRequired]
	public IEnumerable<CsvExpectedFields> ExpectedHeaders { get; set; } = null!;

	[Parameter]
	public EventCallback<Exception> OnParseException { get; set; }

	[Parameter, EditorRequired]
	public Func<IEnumerable<CsvMapperHeader>, TImportMap> GetImportMap { get; set; } = null!;

	[Parameter]
	public EventCallback<IEnumerable<TImportModel>> OnImport { get; set; }

	[Parameter]
	public EventCallback OnImportCanceled { get; set; }

	[Parameter]
	public CsvMapperLocales LocaleStrings { get; set; } = new();
	#endregion

	public List<CsvMapperHeader> Headers { get; set; } = new List<CsvMapperHeader>();

	private void OnFileReset()
	{
		Headers.Clear();
		FileContentBytes = null;
	}

	private Task OnFileRead(Abstractions.File file)
	{
		FileContentBytes = file.Bytes;
		return ParseFileHeaders();
	}

	private Task DelimeterChanged(string? delimeter)
	{
		Delimeter = delimeter;
		return ParseFileHeaders();
	}

	private async Task ParseFileHeaders()
	{
		try
		{
			Headers.Clear();
			if (FileContentBytes is null || string.IsNullOrWhiteSpace(Delimeter))
			{
				if (OnImportCanceled.HasDelegate)
				{
					await OnImportCanceled.InvokeAsync();
				}
				return;
			}

			var config = new CsvConfiguration(CultureInfo.InvariantCulture)
			{
				Delimiter = Delimeter,
				IgnoreBlankLines = true,
				HasHeaderRecord = true
			};

			using var reader = new StreamReader(new MemoryStream(FileContentBytes), Encoding.Default);
			using (var csv = new CsvReader(reader, config))
			{
				csv.Read();
				var headers = csv.ReadHeader();
				string[]? headerRow = csv.HeaderRecord;
				if (headerRow is not null)
				{
					Headers.AddRange(headerRow.Select(header => new CsvMapperHeader(header)));
				}
			}
			// if (_mapperRef is not null)
			// 	await _mapperRef.UpdateViewAsync();
		}
		catch (Exception exc)
		{
			if (OnParseException.HasDelegate)
			{
				await OnParseException.InvokeAsync(exc);
			}
		}
	}

	private async Task Import()
	{
		try
		{
			if (FileContentBytes is null || !OnImport.HasDelegate || string.IsNullOrWhiteSpace(Delimeter))
			{
				if (OnImportCanceled.HasDelegate)
				{
					await OnImportCanceled.InvokeAsync();
				}
				return;
			}

			var map = GetImportMap.Invoke(Headers);
			var config = new CsvConfiguration(CultureInfo.InvariantCulture)
			{
				Delimiter = Delimeter,
				IgnoreBlankLines = true,
				HasHeaderRecord = true
			};

			using var reader = new StreamReader(new MemoryStream(FileContentBytes), Encoding.Default);
			using (var csv = new CsvReader(reader, config))
			{
				csv.Context.RegisterClassMap(map);
				var records = csv.GetRecords<TImportModel>().ToList();
				await OnImport.InvokeAsync(records);
			}
		}
		catch (Exception exc)
		{
			if (OnParseException.HasDelegate)
			{
				await OnParseException.InvokeAsync(exc);
			}
		}
	}
}
